// src/dashboard/pages/UserManagement.jsx
import React, { useState, useEffect } from 'react';
import {
  FiPlus,
  FiDownload,
  FiUpload,
  FiAlertTriangle,
  FiUser,
  FiMail,
  FiCalendar,
  FiSettings
} from 'react-icons/fi';
import { DataTable, Modal, LoadingSpinner, useToast } from '../components';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showLimitsModal, setShowLimitsModal] = useState(false);


  const [filters, setFilters] = useState({
    search: '',
    planFilter: '',
    statusFilter: '',
    page: 1
  });

  const { success, error: showError } = useToast();

  useEffect(() => {
    fetchUsers();
  }, [filters]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: filters.page,
        search: filters.search,
        planFilter: filters.planFilter,
        statusFilter: filters.statusFilter
      });

      const response = await fetch(`http://localhost:3001/api/dashboard/users?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const result = await response.json();
      setUsers(result.data.users);
      setPagination(result.data.pagination);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleRowAction = async (action, user) => {
    try {
      switch (action) {
        case 'view':
          setSelectedUser(user);
          setShowUserModal(true);
          break;
        case 'edit':
          setSelectedUser(user);
          setShowUserModal(true);
          break;
        case 'limits':
          setSelectedUser(user);
          setShowLimitsModal(true);
          break;
        case 'delete':
          if (window.confirm('Are you sure you want to delete this user?')) {
            await deleteUser(user.id || user._id);
          }
          break;
        default:
          console.log('Unknown action:', action);
          break;
      }
    } catch (error) {
      console.error('Error in handleRowAction:', error);
      showError('An error occurred while performing the action');
    }
  };

  const deleteUser = async (userId) => {
    try {
      const response = await fetch(`http://localhost:3001/api/dashboard/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete user');
      }

      fetchUsers(); // Refresh the list
      success('User deleted successfully');
    } catch (err) {
      console.error('Error deleting user:', err);
      showError('Failed to delete user: ' + err.message);
    }
  };

  const updateUserLimits = async (userId, limits) => {
    try {
      console.log('updateUserLimits called with:', { userId, limits }); // Debug log

      // Validate userId
      if (!userId || userId === 'undefined' || userId === null) {
        throw new Error('Invalid user ID provided');
      }

      // Validate limits
      if (!limits || typeof limits !== 'object') {
        throw new Error('Invalid limits data provided');
      }

      const response = await fetch(`http://localhost:3001/api/dashboard/users/${userId}/limits`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({ limits })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to update user limits`);
      }

      const result = await response.json();
      console.log('User limits updated successfully:', result); // Debug log

      setShowLimitsModal(false);
      fetchUsers(); // Refresh the list
      success('User limits updated successfully');
    } catch (err) {
      console.error('Error updating user limits:', err);
      showError('Failed to update user limits: ' + err.message);
    }
  };

  const columns = [
    {
      key: 'name',
      label: 'Name',
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <FiUser className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{value || 'Unknown'}</p>
            <p className="text-sm text-gray-500">{row.email}</p>
          </div>
        </div>
      )
    },
    {
      key: 'subscription.planName',
      label: 'Plan',
      type: 'badge',
      getBadgeClass: (value) => {
        switch (value) {
          case 'Pro':
            return 'bg-blue-100 text-blue-800';
          case 'Enterprise':
            return 'bg-purple-100 text-purple-800';
          default:
            return 'bg-gray-100 text-gray-800';
        }
      },
      render: (_, row) => {
        const planName = row.subscription?.planName || 'Starter';
        const badgeClass = planName === 'Pro'
          ? 'bg-blue-100 text-blue-800'
          : planName === 'Enterprise'
          ? 'bg-purple-100 text-purple-800'
          : 'bg-gray-100 text-gray-800';

        return (
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${badgeClass}`}>
            {planName}
          </span>
        );
      }
    },
    {
      key: 'isVerified',
      label: 'Status',
      type: 'badge',
      getBadgeClass: (value) => value ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800',
      render: (value) => (
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
        }`}>
          {value ? 'Verified' : 'Pending'}
        </span>
      )
    },
    {
      key: 'createdAt',
      label: 'Joined',
      type: 'date',
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'activityCount30Days',
      label: 'Activity (30d)',
      render: (value) => value || 0
    }
  ];

  const actionItems = [
    { label: 'View Details', icon: FiUser, action: 'view' },
    { label: 'Edit User', icon: FiSettings, action: 'edit' },
    { label: 'Manage Limits', icon: FiUpload, action: 'limits' },
    { label: 'Delete User', icon: FiAlertTriangle, action: 'delete', className: 'text-red-600' }
  ];

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center gap-3">
          <FiAlertTriangle className="w-5 h-5 text-red-600" />
          <h3 className="text-lg font-medium text-red-800">Error Loading Users</h3>
        </div>
        <p className="mt-2 text-red-700">{error}</p>
        <button
          onClick={fetchUsers}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600">Manage user accounts, subscriptions, and limits</p>
        </div>

        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <FiDownload className="w-4 h-4" />
            Export
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <FiPlus className="w-4 h-4" />
            Add User
          </button>

        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Plan Filter</label>
            <select
              value={filters.planFilter}
              onChange={(e) => setFilters(prev => ({ ...prev, planFilter: e.target.value, page: 1 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Plans</option>
              <option value="Starter">Starter</option>
              <option value="Pro">Pro</option>
              <option value="Enterprise">Enterprise</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status Filter</label>
            <select
              value={filters.statusFilter}
              onChange={(e) => setFilters(prev => ({ ...prev, statusFilter: e.target.value, page: 1 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Status</option>
              <option value="verified">Verified</option>
              <option value="unverified">Unverified</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <DataTable
        data={users}
        columns={columns}
        loading={loading}
        pagination={pagination}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        onRowAction={handleRowAction}
        searchPlaceholder="Search users by name or email..."
        actionItems={actionItems}
      />

      {/* User Details Modal */}
      <Modal
        isOpen={showUserModal}
        onClose={() => {
          setShowUserModal(false);
          setSelectedUser(null);
        }}
        title={selectedUser ? `User Details - ${selectedUser.name || selectedUser.email}` : "User Details"}
        size="lg"
      >
        {selectedUser ? (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <FiUser className="w-4 h-4" />
                  Basic Information
                </h4>
                <div className="space-y-4">
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Name</label>
                    <p className="text-sm text-gray-900 mt-1">{selectedUser.name || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Email</label>
                    <p className="text-sm text-gray-900 mt-1">{selectedUser.email}</p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Status</label>
                    <div className="mt-1">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        selectedUser.isVerified
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {selectedUser.isVerified ? 'Verified' : 'Pending Verification'}
                      </span>
                    </div>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Joined</label>
                    <p className="text-sm text-gray-900 mt-1 flex items-center gap-1">
                      <FiCalendar className="w-3 h-3" />
                      {new Date(selectedUser.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <FiSettings className="w-4 h-4" />
                  Subscription Details
                </h4>
                <div className="space-y-4">
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Plan</label>
                    <div className="mt-1">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        selectedUser.subscription?.planName === 'Pro'
                          ? 'bg-blue-100 text-blue-800'
                          : selectedUser.subscription?.planName === 'Enterprise'
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {selectedUser.subscription?.planName || 'Starter'}
                      </span>
                    </div>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Status</label>
                    <p className="text-sm text-gray-900 mt-1 capitalize">
                      {selectedUser.subscription?.status || 'active'}
                    </p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Activity (30 days)</label>
                    <p className="text-sm text-gray-900 mt-1">
                      {selectedUser.activityCount30Days || 0} actions
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Info */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-900 mb-3">Quick Actions</h4>
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    setShowUserModal(false);
                    setShowLimitsModal(true);
                  }}
                  className="px-3 py-1.5 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 transition-colors"
                >
                  Manage Limits
                </button>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(selectedUser.email);
                    success('Email copied to clipboard');
                  }}
                  className="px-3 py-1.5 bg-gray-600 text-white text-xs rounded-md hover:bg-gray-700 transition-colors"
                >
                  Copy Email
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No user selected</p>
          </div>
        )}
      </Modal>

      {/* Limits Management Modal */}
      <Modal
        isOpen={showLimitsModal}
        onClose={() => {
          setShowLimitsModal(false);
          setSelectedUser(null);
        }}
        title={selectedUser ? `Manage Limits - ${selectedUser.name || selectedUser.email}` : "Manage User Limits"}
        size="lg"
      >
        {selectedUser ? (
          <LimitsForm
            user={selectedUser}
            onSave={updateUserLimits}
            onCancel={() => {
              setShowLimitsModal(false);
              setSelectedUser(null);
            }}
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No user selected</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

// Limits Form Component
const LimitsForm = ({ user, onSave, onCancel }) => {
  // Get user ID with fallback
  const userId = user.id || user._id;

  const [limits, setLimits] = useState({
    planName: user.subscription?.planName || 'Starter',
    // Free tier limits
    freeTierUploadCount: user.subscription?.freeTierUploadCount || 0,
    freeTierMessageCount: user.subscription?.freeTierMessageCount || 0,
    freeTierBusinessPlanCount: user.subscription?.freeTierBusinessPlanCount || 0,
    freeTierInvestorPitchCount: user.subscription?.freeTierInvestorPitchCount || 0,
    freeTierBusinessQACount: user.subscription?.freeTierBusinessQACount || 0,
    // Pro tier limits
    proTierUploadCount: user.subscription?.proTierUploadCount || 0,
    proTierMessageCount: user.subscription?.proTierMessageCount || 0,
    proTierBusinessPlanCount: user.subscription?.proTierBusinessPlanCount || 0,
    proTierInvestorPitchCount: user.subscription?.proTierInvestorPitchCount || 0,
    proTierBusinessQACount: user.subscription?.proTierBusinessQACount || 0
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onSave(userId, limits);
    } catch (error) {
      console.error('Error saving limits:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Current User Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-gray-900 mb-2">User Information</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Name:</span>
            <span className="ml-2 text-gray-900">{user.name || 'Not provided'}</span>
          </div>
          <div>
            <span className="text-gray-500">Email:</span>
            <span className="ml-2 text-gray-900">{user.email}</span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Subscription Plan</label>
          <select
            value={limits.planName}
            onChange={(e) => setLimits(prev => ({ ...prev, planName: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          >
            <option value="Starter">Starter Plan</option>
            <option value="Pro">Pro Plan</option>
            <option value="Enterprise">Enterprise Plan</option>
          </select>
        </div>

        {/* Free Tier Limits */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h5 className="text-sm font-semibold text-gray-900 mb-4">Free Tier Limits</h5>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload Count
                <span className="text-xs text-gray-500 block">Files uploaded</span>
              </label>
              <input
                type="number"
                min="0"
                value={limits.freeTierUploadCount}
                onChange={(e) => setLimits(prev => ({ ...prev, freeTierUploadCount: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Message Count
                <span className="text-xs text-gray-500 block">AI messages sent</span>
              </label>
              <input
                type="number"
                min="0"
                value={limits.freeTierMessageCount}
                onChange={(e) => setLimits(prev => ({ ...prev, freeTierMessageCount: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Plans
                <span className="text-xs text-gray-500 block">Business plans generated</span>
              </label>
              <input
                type="number"
                min="0"
                value={limits.freeTierBusinessPlanCount}
                onChange={(e) => setLimits(prev => ({ ...prev, freeTierBusinessPlanCount: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Investor Pitches
                <span className="text-xs text-gray-500 block">Investor pitches generated</span>
              </label>
              <input
                type="number"
                min="0"
                value={limits.freeTierInvestorPitchCount}
                onChange={(e) => setLimits(prev => ({ ...prev, freeTierInvestorPitchCount: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Q&A
                <span className="text-xs text-gray-500 block">Business Q&A sessions</span>
              </label>
              <input
                type="number"
                min="0"
                value={limits.freeTierBusinessQACount}
                onChange={(e) => setLimits(prev => ({ ...prev, freeTierBusinessQACount: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>
          </div>
        </div>

        {/* Pro Tier Limits */}
        <div className="bg-purple-50 rounded-lg p-4">
          <h5 className="text-sm font-semibold text-gray-900 mb-4">Pro Tier Limits</h5>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload Count
                <span className="text-xs text-gray-500 block">Files uploaded</span>
              </label>
              <input
                type="number"
                min="0"
                value={limits.proTierUploadCount}
                onChange={(e) => setLimits(prev => ({ ...prev, proTierUploadCount: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Message Count
                <span className="text-xs text-gray-500 block">AI messages sent</span>
              </label>
              <input
                type="number"
                min="0"
                value={limits.proTierMessageCount}
                onChange={(e) => setLimits(prev => ({ ...prev, proTierMessageCount: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Plans
                <span className="text-xs text-gray-500 block">Business plans generated</span>
              </label>
              <input
                type="number"
                min="0"
                value={limits.proTierBusinessPlanCount}
                onChange={(e) => setLimits(prev => ({ ...prev, proTierBusinessPlanCount: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Investor Pitches
                <span className="text-xs text-gray-500 block">Investor pitches generated</span>
              </label>
              <input
                type="number"
                min="0"
                value={limits.proTierInvestorPitchCount}
                onChange={(e) => setLimits(prev => ({ ...prev, proTierInvestorPitchCount: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Q&A
                <span className="text-xs text-gray-500 block">Business Q&A sessions</span>
              </label>
              <input
                type="number"
                min="0"
                value={limits.proTierBusinessQACount}
                onChange={(e) => setLimits(prev => ({ ...prev, proTierBusinessQACount: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default UserManagement;  