// models/User.js
import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

/**
 * Defines the schema for a user's subscription details, including plan info,
 * payment details, usage counts for different tiers, and reset dates for
 * periodic limits on the Pro plan.
 */
const subscriptionSchema = new mongoose.Schema({
  planName: {
    type: String,
    enum: ['Starter', 'Pro', 'Enterprise', null],
    default: 'Starter',
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'cancelled', 'pending_payment'],
    default: 'active',
  },
  paypalOrderId: {
    type: String,
    required: false,
  },
  startDate: {
    type: Date,
    default: null,
  },
  endDate: {
    type: Date,
    default: null,
  },
  lastPaymentDate: {
    type: Date,
    default: null,
  },

  // --- FREE TIER USAGE COUNTS ---
  freeTierUploadCount: { type: Number, default: 0 },
  freeTierBusinessPlanCount: { type: Number, default: 0 },
  freeTierInvestorPitchCount: { type: Number, default: 0 },
  freeTierBusinessQACount: { type: Number, default: 0 },
  freeTierMessageCount: { type: Number, default: 0 },

  // --- PRO TIER USAGE COUNTS ---
  proTierUploadCount: { type: Number, default: 0 },
  proTierBusinessPlanCount: { type: Number, default: 0 },
  proTierInvestorPitchCount: { type: Number, default: 0 },
  proTierBusinessQACount: { type: Number, default: 0 },
  proTierMessageCount: { type: Number, default: 0 },

  // --- PRO TIER PERIODIC RESET TRACKING ---
  // Stores the date when the monthly/daily period for a feature started.
  businessPlanMonthlyReset: {
    type: Date,
    default: () => new Date(),
  },
  investorPitchMonthlyReset: {
    type: Date,
    default: () => new Date(),
  },
  businessQADailyReset: {
    type: Date,
    default: () => new Date(),
  },

  // --- CUSTOM ADMIN-SET LIMITS (OVERRIDE DEFAULT PLAN LIMITS) ---
  customLimits: {
    uploadLimit: { type: Number, default: null }, // null = use plan default
    messageLimit: { type: Number, default: null },
    businessPlanLimit: { type: Number, default: null },
    investorPitchLimit: { type: Number, default: null },
    businessQALimit: { type: Number, default: null },
  },

}, { _id: false });


/**
 * Defines the main User schema, including personal details, authentication info,
 * and the nested subscription object.
 */
const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: false,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    match: [/\S+@\S+\.\S+/, 'is invalid'],
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  subscription: {
    type: subscriptionSchema,
    default: () => ({
      planName: 'Starter',
      status: 'active',
      startDate: new Date(),
      // Initialize all usage counts
      freeTierUploadCount: 0,
      proTierUploadCount: 0,
      freeTierBusinessPlanCount: 0,
      proTierBusinessPlanCount: 0,
      freeTierInvestorPitchCount: 0,
      proTierInvestorPitchCount: 0,
      freeTierBusinessQACount: 0,
      proTierBusinessQACount: 0,
      freeTierMessageCount: 0,
      proTierMessageCount: 0,
      // Initialize all reset dates
      businessPlanMonthlyReset: new Date(),
      investorPitchMonthlyReset: new Date(),
      businessQADailyReset: new Date(),
    })
  },
}, { timestamps: true });

// Middleware to hash the password before saving a user document
UserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    return next();
  }
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (err) {
    next(err);
  }
});

// Method to compare a candidate password with the user's hashed password
UserSchema.methods.comparePassword = async function (candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    console.error("Error comparing password:", error);
    return false;
  }
};

const User = mongoose.model('User', UserSchema);

export default User;